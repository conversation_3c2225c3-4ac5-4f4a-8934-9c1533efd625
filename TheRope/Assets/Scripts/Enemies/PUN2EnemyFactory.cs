// Copyright Isto Inc.

using ExitGames.Client.Photon;
using Isto.Core.Beings;
using Isto.Core.Networking;
using Photon.Pun;
using Photon.Realtime;
using System.Collections.Generic;
using System.Linq;
using UnityEditor;
using UnityEngine;
using Zenject;

namespace Isto.Core.Photon
{
    public class PUN2EnemyFactory : MonoBehaviour, IEnemyFactory
    {
        [System.Serializable]
        public class EnemyPrefabEntry
        {
            public string EnemyType;
            public BaseEnemyController EnemyPrefab;
        }


        // UNITY HOOKUP

        [SerializeField] private List<EnemyPrefabEntry> _enemyPrefabs = new List<EnemyPrefabEntry>();
        [SerializeField] private PrefabPool _networkPoolingType;
        [SerializeField] private string _enemySpawnTag = "EnemySpawn";


        // OTHER FIELDS

        private INetworkManager _networkManager;
        private DiContainer _container;


        // INJECTION

        [Inject]
        public void Inject(INetworkManager networkManager, DiContainer container)
        {
            _networkManager = networkManager;
            _container = container;
        }


        // LIFECYCLE EVENTS

        private void Awake()
        {
            switch (_networkPoolingType)
            {
                case PrefabPool.Project:
                    PhotonNetwork.PrefabPool = new ProjectAssetPool();
                    break;
                case PrefabPool.Addressables:
                    PhotonNetwork.PrefabPool = new AddressablesPool();
                    break;
                case PrefabPool.None:
                case PrefabPool.Resources:
                default:
                    break;
            }
        }


        // OTHER METHODS

        public GameObject CreateEnemy(string enemyType)
        {
            Vector3 spawnPosition = Vector3.zero;
            Quaternion spawnRotation = Quaternion.identity;

            GameObject spawnPoint = GameObject.FindGameObjectWithTag(_enemySpawnTag);
            if (spawnPoint != null)
            {
                spawnPosition = spawnPoint.transform.position;
                spawnRotation = spawnPoint.transform.rotation;
            }

            return CreateEnemy(enemyType, spawnPosition, spawnRotation);
        }

        public GameObject CreateEnemy(string enemyType, Vector3 position, Quaternion rotation)
        {
            EnemyPrefabEntry entry = _enemyPrefabs.FirstOrDefault(e => e.EnemyType == enemyType);
            if (entry == null || entry.EnemyPrefab == null)
            {
                Debug.LogError($"PUN2EnemyFactory: No prefab found for enemy type '{enemyType}'");
                return null;
            }

            GameObject enemy = null;

            if (!_networkManager.IsMultiplayerAvailable())
            {
                enemy = Instantiate(entry.EnemyPrefab.gameObject, position, rotation);
            }
            else
            {
                object[] customInitData = { enemyType, position.x, position.y, position.z };

                switch (_networkPoolingType)
                {
                    case PrefabPool.None:
                        enemy = CustomPhotonManualSpawn(entry.EnemyPrefab.gameObject, position, rotation, enemyType);
                        break;
                    case PrefabPool.Project:
#if UNITY_EDITOR
                        string path = AssetDatabase.GetAssetPath(entry.EnemyPrefab.gameObject);
                        enemy = PhotonNetwork.Instantiate(path, position, rotation, group: 0, data: customInitData);
#endif
                        break;
                    case PrefabPool.Addressables:
                    case PrefabPool.Resources:
                    default:
                        enemy = PhotonNetwork.Instantiate(entry.EnemyPrefab.name, position, rotation, group: 0, data: customInitData);
                        break;
                }
            }

            _container.InjectGameObject(enemy);

            return enemy;
        }

        // Copied from examples - in case we want to avoid photon's resource based pool without having to
        // build a replacement pool.
        // Warning: UNTESTED!
        public GameObject CustomPhotonManualSpawn(GameObject enemyPrefab, Vector3 position, Quaternion rotation, string enemyType)
        {
            GameObject enemy = Instantiate(enemyPrefab, position, rotation);
            PhotonView photonView = enemy.GetComponent<PhotonView>();

            if (PhotonNetwork.AllocateViewID(photonView))
            {
                object[] data = new object[]
                {
                    enemyType,
                    enemy.transform.position,
                    enemy.transform.rotation,
                    photonView.ViewID
                };

                RaiseEventOptions raiseEventOptions = new RaiseEventOptions
                {
                    Receivers = ReceiverGroup.Others,
                    CachingOption = EventCaching.AddToRoomCache
                };

                SendOptions sendOptions = new SendOptions
                {
                    Reliability = true
                };

                PhotonNetwork.RaiseEvent(PUN2NetworkManager.NetworkEventCodes.CUSTOM_MANUAL_INSTANTIATION, data, raiseEventOptions, sendOptions);
            }
            else
            {
                Debug.LogError("PUN2EnemyFactory: Failed to allocate a ViewId for enemy.");

                Destroy(enemy);
            }

            return enemy;
        }

    }
}