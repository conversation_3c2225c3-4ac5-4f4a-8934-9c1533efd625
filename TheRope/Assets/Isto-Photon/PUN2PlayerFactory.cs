// Copyright Isto Inc.

using ExitGames.Client.Photon;
using Isto.Core.Beings;
using Isto.Core.Networking;
using Photon.Pun;
using Photon.Realtime;
using UnityEditor;
using UnityEngine;
using Zenject;

namespace Isto.Core.Photon
{
    /// <summary>
    /// Handles basic Respawn point logic and network/local player spawning like SimplePlayerFactory, but allows more
    /// configuration for PUN2-specific features. In particular this offers different system options for the PUN2
    /// prefab pool.
    /// </summary>
    public class PUN2PlayerFactory : MonoBehaviour, IPlayerFactory
    {
        // UNITY HOOKUP

        [SerializeField] private GameObject _playerPrefab;
        [SerializeField] private PrefabPool _networkPoolingType;


        // INJECTION

        private INetworkManager _networkManager;
        private DiContainer _container;

        [Inject]
        public void Inject(INetworkManager networkManager,
                           DiContainer container)
        {
            _networkManager = networkManager;
            _container = container;
        }


        // LIFECYCLE EVENTS

        private void Awake()
        {
            switch (_networkPoolingType)
            {
                case PrefabPool.Project:
                    PhotonNetwork.PrefabPool = new ProjectAssetPool(); // not really a pool but works for prototyping
                    break;
                case PrefabPool.Addressables:
                    PhotonNetwork.PrefabPool = new AddressablesPool(); // more serious solution for performance
                    break;
                case PrefabPool.None: // in this case we won't use the system
                case PrefabPool.Resources: // in this case this is the default photon system
                default:
                    break;
            }
        }


        // OTHER METHODS

        public GameObject CreatePlayer()
        {
            GameObject player = null;
            Vector3 spawnPosition = Vector3.zero;
            Quaternion spawnRotation = Quaternion.identity;

            // This is one of the default unity tags so might as well use it
            GameObject respawn = GameObject.FindGameObjectWithTag("Respawn");
            if (respawn != null)
            {
                spawnPosition = respawn.transform.position;
                spawnRotation = respawn.transform.rotation;
            }

            player = CreatePlayer(spawnPosition, spawnRotation);

            return player;
        }

        public GameObject CreatePlayer(Vector3 position, Quaternion rotation)
        {
            GameObject player = null;
            if (!_networkManager.IsMultiplayerAvailable())
            {
                player = Instantiate(_playerPrefab, position, rotation);
            }
            else
            {
                object[] myCustomInitData = { "test123" };

                switch (_networkPoolingType)
                {
                    case PrefabPool.None:
                        player = CustomPhotonManualSpawn(position, rotation);
                        break;
                    case PrefabPool.Project:
#if UNITY_EDITOR
                        string path = AssetDatabase.GetAssetPath(_playerPrefab);
                        player = PhotonNetwork.Instantiate(path, position, rotation, group: 0, data: myCustomInitData);
#endif
                        break;
                    case PrefabPool.Addressables:
                    case PrefabPool.Resources:
                    default:
                        player = PhotonNetwork.Instantiate(_playerPrefab.name, position, rotation, group: 0, data: myCustomInitData);
                        break;
                }
            }

            _container.InjectGameObject(player);

            return player;
        }

        // Copied from examples - in case we want to avoid photon's resource based pool without having to
        // build a replacement pool.
        // Warning: UNTESTED!
        public GameObject CustomPhotonManualSpawn(Vector3 position, Quaternion rotation)
        {
            GameObject player = Instantiate(_playerPrefab, position, rotation);
            PhotonView photonView = player.GetComponent<PhotonView>();

            if (PhotonNetwork.AllocateViewID(photonView))
            {
                object[] data = new object[]
                {
                    player.transform.position,
                    player.transform.rotation,
                    photonView.ViewID
                };

                RaiseEventOptions raiseEventOptions = new RaiseEventOptions
                {
                    Receivers = ReceiverGroup.Others,
                    CachingOption = EventCaching.AddToRoomCache
                };

                SendOptions sendOptions = new SendOptions
                {
                    Reliability = true
                };

                PhotonNetwork.RaiseEvent(PUN2NetworkManager.NetworkEventCodes.CUSTOM_MANUAL_INSTANTIATION,
                                         data, raiseEventOptions, sendOptions);
            }
            else
            {
                Debug.LogError("PUN2PlayerFactory: Failed to allocate a ViewId for player.");

                Destroy(player);
            }

            return player;

            throw new System.NotImplementedException();
        }

        // For the code example above you also would need to hook this up to photon network event callbacks
        // or use our custom event manager instead
        public void OnEvent(EventData photonEvent)
        {
            if (photonEvent.Code == PUN2NetworkManager.NetworkEventCodes.CUSTOM_MANUAL_INSTANTIATION)
            {
                object[] data = (object[])photonEvent.CustomData;

                GameObject player = (GameObject)Instantiate(_playerPrefab, (Vector3)data[0], (Quaternion)data[1]);
                PhotonView photonView = player.GetComponent<PhotonView>();
                photonView.ViewID = (int)data[2];
            }

            throw new System.NotImplementedException();
        }
    }
}