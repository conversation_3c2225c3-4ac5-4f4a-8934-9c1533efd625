// Copyright Isto Inc.

using Isto.Core.StateMachine;
using UnityEngine;

namespace Isto.Core.Beings
{
    /// <summary>
    /// Base class for all enemy controllers, providing common functionality
    /// </summary>
    public abstract class BaseEnemyController : MonoStateMachine, IEnemyController
    {
        // OTHER FIELDS

        private Transform _currentTarget;


        // PROPERTIES

        public Transform CurrentTarget => _currentTarget;


        // LIFECYCLE EVENTS

        protected override void Start()
        {
            base.Start();
            RegisterEvents();
        }

        protected virtual void OnDestroy()
        {
            UnregisterEvents();
        }


        // EVENT HANDLING

        protected virtual void RegisterEvents()
        {
        }

        protected virtual void UnregisterEvents()
        {
        }


        // INTERFACE IMPLEMENTATION

        public virtual void SetTarget(Transform target)
        {
            _currentTarget = target;
        }
    }
}