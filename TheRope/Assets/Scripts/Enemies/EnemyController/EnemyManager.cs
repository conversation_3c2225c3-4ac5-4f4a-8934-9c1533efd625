// Copyright Isto Inc.

using Isto.Core.Networking;
using Photon.Pun;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Zenject;

namespace Isto.Core.Beings
{
    /// <summary>
    /// Manages all enemy instances in the game with networking support, similar to PlayerManager
    /// </summary>
    public class EnemyManager : MonoBehaviour
    {
        // UNITY HOOKUP

        [Header("Network Enemy Spawning Settings")]
        [SerializeField] private string _defaultEnemyTypeToSpawn = "Spider";
        [SerializeField] private bool _onlyMasterClientSpawns = true;
        [SerializeField] private bool _autoSpawnOnRoomJoin = true;


        // OTHER FIELDS

        private List<IEnemyController> _allEnemyInstances;


        // PROPERTIES

        public virtual List<IEnemyController> AllEnemies => _allEnemyInstances;
        public virtual int TotalEnemyCount => _allEnemyInstances.Count;


        // INJECTION

        private IEnemyFactory _enemyFactory;
        private INetworkManager _networkManager;

        [Inject]
        public void Inject(IEnemyFactory enemyFactory,
                          INetworkManager networkManager)
        {
            _enemyFactory = enemyFactory;
            _networkManager = networkManager;
        }


        // LIFECYCLE EVENTS

        private void Awake()
        {
            _allEnemyInstances = new List<IEnemyController>();
        }

        private void OnDestroy()
        {
            UnregisterEvents();
        }

        private void Start()
        {
            // Find any existing enemies in the scene and register them
            IEnemyController[] existingEnemies = FindObjectsByType<MonoBehaviour>(FindObjectsSortMode.None)
                .OfType<IEnemyController>()
                .ToArray();

            foreach (IEnemyController enemy in existingEnemies)
            {
                RegisterEnemy(enemy);
            }

            RegisterEvents();
        }


        // EVENT HANDLING

        private void RegisterEvents()
        {
            Events.SubscribeWithParams(Events.GAMEOBJECT_SPAWNED_FROM_NETWORK, Events_OnGameObjectSpawned);
            Events.Subscribe(Events.NETWORK_ROOM_JOINED, OnNetworkRoomJoined);
        }

        private void UnregisterEvents()
        {
            Events.UnSubscribeWithParams(Events.GAMEOBJECT_SPAWNED_FROM_NETWORK, Events_OnGameObjectSpawned);
            Events.UnSubscribe(Events.NETWORK_ROOM_JOINED, OnNetworkRoomJoined);
        }

        private void Events_OnGameObjectSpawned(object[] args)
        {
            GameObject spawnedObj = (GameObject)args[0];
            IEnemyController enemyComponent = spawnedObj.GetComponent<IEnemyController>();
            if (enemyComponent != null)
            {
                OnEnemySpawned(enemyComponent);
            }
        }

        private void OnEnemySpawned(IEnemyController enemyController)
        {
            RegisterEnemy(enemyController);
        }

        private void OnNetworkRoomJoined()
        {
            if (_autoSpawnOnRoomJoin && ShouldSpawnEnemies())
            {
                // Delay spawning slightly to ensure all players are properly connected
                Invoke(nameof(SpawnInitialEnemies), 1f);
            }
        }


        // ENEMY MANAGEMENT METHODS

        private GameObject CreateEnemy(string enemyType)
        {
            GameObject enemy = _enemyFactory.CreateEnemy(enemyType);
            if (enemy != null)
            {
                IEnemyController enemyController = enemy.GetComponent<IEnemyController>();
                if (enemyController != null)
                {
                    RegisterEnemy(enemyController);
                }
            }
            else
            {
                Debug.LogError($"EnemyManager: Failed to create enemy of type {enemyType}");
            }

            return enemy;
        }


        protected virtual void RegisterEnemy(IEnemyController enemy)
        {
            if (!_allEnemyInstances.Contains(enemy))
            {
                _allEnemyInstances.Add(enemy);
            }
        }

        // NOTE: This isn't used yet as we don't destroy enemies yet. This may come in the future though.
        protected virtual void UnregisterEnemy(IEnemyController enemy)
        {
            _allEnemyInstances.Remove(enemy);
        }


        private bool ShouldSpawnEnemies()
        {
            if (!_networkManager.IsMultiplayerAvailable())
            {
                return true; // Single player mode
            }

            if (_onlyMasterClientSpawns)
            {
                return PhotonNetwork.IsMasterClient;
            }

            return true; // Allow any client to spawn
        }

        private void SpawnInitialEnemies()
        {
            if (!ShouldSpawnEnemies())
                return;

            SpawnNetworkEnemy(_defaultEnemyTypeToSpawn);
        }

        private void SpawnNetworkEnemy(string enemyType)
        {
            if (!ShouldSpawnEnemies())
            {
                Debug.LogWarning($"EnemyManager: Client not authorized to spawn enemies. Master client: {PhotonNetwork.IsMasterClient}");
            }

            // Spawn the enemy (will be networked if using PUN2EnemyFactory)
            GameObject enemy = CreateEnemy(enemyType);

            if (enemy != null)
            {
                string networkStatus = _networkManager.IsMultiplayerAvailable() ? "networked" : "local";
                Debug.Log($"EnemyManager: Spawned {networkStatus} {enemyType}");
            }
            else
            {
                Debug.LogError($"EnemyManager: Failed to spawn enemy of type {enemyType}");
            }
        }
    }
}